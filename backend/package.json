{"name": "job-manager-backend", "version": "1.0.0", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "better-sqlite3": "^9.2.2", "multer": "^2.0.0", "xlsx": "^0.18.5", "dayjs": "^1.11.10", "joi": "^17.11.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/better-sqlite3": "^7.6.8", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "typescript": "^5.3.3", "ts-node-dev": "^2.0.0"}}