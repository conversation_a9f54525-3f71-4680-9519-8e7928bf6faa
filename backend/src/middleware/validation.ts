import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

const applicationSchema = Joi.object({
  company_id: Joi.number().integer().positive().optional(),
  company_name: Joi.string().min(1).max(255).when('company_id', {
    is: Joi.exist(),
    then: Joi.optional(),
    otherwise: Joi.required()
  }),
  company_industry: Joi.string().max(100).optional().allow(''),
  company_location: Joi.string().max(255).optional().allow(''),
  position: Joi.string().min(1).max(255).required(),
  application_date: Joi.date().iso().required(),
  status: Joi.string().valid(
    'applied', 'screening', 'written_test', 'interview',
    'final_interview', 'offer', 'rejected', 'withdrawn'
  ).required(),
  salary_range: Joi.string().max(100).optional().allow(''),
  work_location: Joi.string().max(255).optional().allow(''),
  job_description: Joi.string().optional().allow(''),
  requirements_keywords: Joi.string().optional().allow(''),
  referrer_name: Joi.string().max(100).optional().allow(''),
  referrer_contact: Joi.string().max(255).optional().allow(''),
  application_link: Joi.string().uri().optional().allow(''),
  notes: Joi.string().optional().allow('')
});

export const validateApplication = (req: Request, res: Response, next: NextFunction) => {
  const { error } = applicationSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      error: 'Validation error',
      details: error.details.map(detail => detail.message)
    });
  }
  
  next();
};

const companySchema = Joi.object({
  name: Joi.string().min(1).max(255).required(),
  industry: Joi.string().max(100).optional(),
  size: Joi.string().max(50).optional(),
  location: Joi.string().max(255).optional(),
  website: Joi.string().uri().optional()
});

export const validateCompany = (req: Request, res: Response, next: NextFunction) => {
  const { error } = companySchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      error: 'Validation error',
      details: error.details.map(detail => detail.message)
    });
  }
  
  next();
};