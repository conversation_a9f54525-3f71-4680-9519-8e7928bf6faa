import { db } from '../database/database';
import { Application, ApplicationWithDetails } from '../types';

export class ApplicationModel {
  static create(application: Omit<Application, 'id' | 'created_at' | 'updated_at'>): Application {
    const stmt = db.prepare(`
      INSERT INTO applications (
        company_id, position, application_date, status, salary_range,
        work_location, job_description, requirements_keywords,
        referrer_name, referrer_contact, application_link, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      application.company_id,
      application.position,
      application.application_date,
      application.status,
      application.salary_range,
      application.work_location,
      application.job_description,
      application.requirements_keywords,
      application.referrer_name,
      application.referrer_contact,
      application.application_link,
      application.notes
    );

    return this.findById(result.lastInsertRowid as number)!;
  }

  static findById(id: number): ApplicationWithDetails | null {
    const stmt = db.prepare(`
      SELECT a.*, c.name as company_name, c.industry, c.location as company_location
      FROM applications a
      LEFT JOIN companies c ON a.company_id = c.id
      WHERE a.id = ?
    `);
    
    const application = stmt.get(id) as any;
    if (!application) return null;

    // Get related data
    const writtenTests = db.prepare('SELECT * FROM written_tests WHERE application_id = ?').all(id);
    const interviews = db.prepare('SELECT * FROM interviews WHERE application_id = ? ORDER BY round_number').all(id);

    return {
      ...application,
      written_tests: writtenTests,
      interviews: interviews
    } as ApplicationWithDetails;
  }

  static findAll(filters?: {
    status?: string;
    company?: string;
    position?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Application[] {
    let query = `
      SELECT a.*, c.name as company_name, c.industry, c.location as company_location
      FROM applications a
      LEFT JOIN companies c ON a.company_id = c.id
      WHERE 1=1
    `;
    
    const params: any[] = [];

    if (filters?.status) {
      query += ' AND a.status = ?';
      params.push(filters.status);
    }

    if (filters?.company) {
      query += ' AND c.name LIKE ?';
      params.push(`%${filters.company}%`);
    }

    if (filters?.position) {
      query += ' AND a.position LIKE ?';
      params.push(`%${filters.position}%`);
    }

    if (filters?.dateFrom) {
      query += ' AND a.application_date >= ?';
      params.push(filters.dateFrom);
    }

    if (filters?.dateTo) {
      query += ' AND a.application_date <= ?';
      params.push(filters.dateTo);
    }

    query += ' ORDER BY a.application_date DESC';

    const stmt = db.prepare(query);
    return stmt.all(...params) as Application[];
  }

  static update(id: number, application: Partial<Application>): Application | null {
    const fields = Object.keys(application).filter(key => !['id', 'created_at', 'updated_at'].includes(key));
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    
    const stmt = db.prepare(`
      UPDATE applications 
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `);
    
    const values = fields.map(field => (application as any)[field]);
    stmt.run(...values, id);
    
    return this.findById(id);
  }

  static delete(id: number): boolean {
    const stmt = db.prepare('DELETE FROM applications WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  static getStatistics() {
    const statusStats = db.prepare(`
      SELECT status, COUNT(*) as count 
      FROM applications 
      GROUP BY status
    `).all();

    const monthlyStats = db.prepare(`
      SELECT 
        strftime('%Y-%m', application_date) as month,
        COUNT(*) as count
      FROM applications 
      GROUP BY strftime('%Y-%m', application_date)
      ORDER BY month DESC
      LIMIT 12
    `).all();

    const totalApplications = db.prepare('SELECT COUNT(*) as count FROM applications').get() as { count: number };

    return {
      statusDistribution: statusStats,
      monthlyApplications: monthlyStats,
      totalApplications: totalApplications.count
    };
  }
}