import Database from 'better-sqlite3';
import { readFileSync } from 'fs';
import { join } from 'path';

export class DatabaseManager {
  private db: Database.Database;

  constructor(dbPath: string = 'job_manager.db') {
    this.db = new Database(dbPath);
    this.db.pragma('journal_mode = WAL');
    this.db.pragma('foreign_keys = ON');
    this.initializeSchema();
  }

  private initializeSchema(): void {
    const schemaPath = join(__dirname, 'schema.sql');
    const schema = readFileSync(schemaPath, 'utf-8');
    this.db.exec(schema);

    // Run migrations
    this.runMigrations();
  }

  private runMigrations(): void {
    // Check if application_link column exists, if not add it
    try {
      const tableInfo = this.db.prepare("PRAGMA table_info(applications)").all() as any[];
      const hasApplicationLink = tableInfo.some(column => column.name === 'application_link');

      if (!hasApplicationLink) {
        this.db.exec('ALTER TABLE applications ADD COLUMN application_link TEXT');
        console.log('Added application_link column to applications table');
      }
    } catch (error) {
      console.error('Migration error:', error);
    }
  }

  getDatabase(): Database.Database {
    return this.db;
  }

  close(): void {
    this.db.close();
  }
}

export const dbManager = new DatabaseManager();
export const db = dbManager.getDatabase();