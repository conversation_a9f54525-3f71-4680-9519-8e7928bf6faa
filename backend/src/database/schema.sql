-- Companies table
CREATE TABLE IF NOT EXISTS companies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    industry TEXT,
    size TEXT,
    location TEXT,
    website TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Job applications table
CREATE TABLE IF NOT EXISTS applications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    position TEXT NOT NULL,
    application_date DATE NOT NULL,
    status TEXT NOT NULL DEFAULT 'applied',
    salary_range TEXT,
    work_location TEXT,
    job_description TEXT,
    requirements_keywords TEXT,
    referrer_name TEXT,
    referrer_contact TEXT,
    application_link TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE
);



-- Written tests table
CREATE TABLE IF NOT EXISTS written_tests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    application_id INTEGER NOT NULL,
    test_date DATETIME,
    test_format TEXT,
    test_type TEXT,
    key_topics TEXT,
    duration_minutes INTEGER,
    result TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications (id) ON DELETE CASCADE
);

-- Interviews table
CREATE TABLE IF NOT EXISTS interviews (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    application_id INTEGER NOT NULL,
    round_number INTEGER NOT NULL,
    round_name TEXT,
    interview_date DATETIME,
    interviewer_name TEXT,
    interviewer_position TEXT,
    interview_format TEXT,
    technical_questions TEXT,
    project_questions TEXT,
    behavioral_questions TEXT,
    my_questions TEXT,
    self_assessment TEXT,
    result TEXT,
    feedback TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications (id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_applications_company_id ON applications(company_id);
CREATE INDEX IF NOT EXISTS idx_applications_status ON applications(status);
CREATE INDEX IF NOT EXISTS idx_applications_date ON applications(application_date);
CREATE INDEX IF NOT EXISTS idx_written_tests_application_id ON written_tests(application_id);
CREATE INDEX IF NOT EXISTS idx_interviews_application_id ON interviews(application_id);