import { Router } from 'express';
import { ApplicationModel } from '../models/Application';
import { CompanyModel } from '../models/Company';
import { validateApplication } from '../middleware/validation';

const router = Router();

// GET /api/applications/stats/overview - Get application statistics (must be before /:id route)
router.get('/stats/overview', (req, res) => {
  try {
    const stats = ApplicationModel.getStatistics();
    res.json(stats);
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({ error: 'Failed to fetch statistics' });
  }
});

// GET /api/applications - Get all applications with optional filters
router.get('/', (req, res) => {
  try {
    const filters = {
      status: req.query.status as string,
      company: req.query.company as string,
      position: req.query.position as string,
      dateFrom: req.query.dateFrom as string,
      dateTo: req.query.dateTo as string,
    };

    const applications = ApplicationModel.findAll(filters);
    res.json(applications);
  } catch (error) {
    console.error('Error fetching applications:', error);
    res.status(500).json({ error: 'Failed to fetch applications' });
  }
});

// GET /api/applications/:id - Get application by ID with details
router.get('/:id', (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const application = ApplicationModel.findById(id);
    
    if (!application) {
      return res.status(404).json({ error: 'Application not found' });
    }
    
    res.json(application);
  } catch (error) {
    console.error('Error fetching application:', error);
    res.status(500).json({ error: 'Failed to fetch application' });
  }
});

// POST /api/applications - Create new application
router.post('/', validateApplication, (req, res) => {
  try {
    const applicationData = req.body;
    
    // Check if company exists, create if not
    let company = CompanyModel.findByName(applicationData.company_name);
    if (!company) {
      company = CompanyModel.create({
        name: applicationData.company_name,
        industry: applicationData.company_industry,
        location: applicationData.company_location
      });
    }
    
    applicationData.company_id = company.id;
    delete applicationData.company_name;
    delete applicationData.company_industry;
    delete applicationData.company_location;
    
    const application = ApplicationModel.create(applicationData);
    res.status(201).json(application);
  } catch (error) {
    console.error('Error creating application:', error);
    res.status(500).json({ error: 'Failed to create application' });
  }
});

// PUT /api/applications/:id - Update application
router.put('/:id', validateApplication, (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const applicationData = req.body;

    // Handle company information for updates
    if (applicationData.company_name) {
      let company = CompanyModel.findByName(applicationData.company_name);
      if (!company) {
        company = CompanyModel.create({
          name: applicationData.company_name,
          industry: applicationData.company_industry,
          location: applicationData.company_location
        });
      }
      applicationData.company_id = company.id;
    }

    // Remove company fields that shouldn't be in applications table
    delete applicationData.company_name;
    delete applicationData.company_industry;
    delete applicationData.company_location;

    const application = ApplicationModel.update(id, applicationData);

    if (!application) {
      return res.status(404).json({ error: 'Application not found' });
    }

    res.json(application);
  } catch (error) {
    console.error('Error updating application:', error);
    res.status(500).json({ error: 'Failed to update application' });
  }
});

// DELETE /api/applications/:id - Delete application
router.delete('/:id', (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const deleted = ApplicationModel.delete(id);
    
    if (!deleted) {
      return res.status(404).json({ error: 'Application not found' });
    }
    
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting application:', error);
    res.status(500).json({ error: 'Failed to delete application' });
  }
});

export default router;