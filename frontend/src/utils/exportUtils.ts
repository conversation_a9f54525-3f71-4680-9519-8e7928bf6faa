import { Application, STATUS_OPTIONS } from '../types';
import dayjs from 'dayjs';

export const exportToMarkdown = (applications: Application[]): string => {
  if (applications.length === 0) {
    return '# 求职投递记录\n\n暂无投递记录。';
  }

  const getStatusLabel = (status: string) => {
    const option = STATUS_OPTIONS.find(opt => opt.value === status);
    return option ? option.label : status;
  };

  let markdown = '# 求职投递记录\n\n';
  markdown += `> 导出时间: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}\n`;
  markdown += `> 总记录数: ${applications.length}\n\n`;

  // 统计信息
  const statusStats = applications.reduce((acc, app) => {
    acc[app.status] = (acc[app.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  markdown += '## 📊 状态统计\n\n';
  Object.entries(statusStats).forEach(([status, count]) => {
    markdown += `- **${getStatusLabel(status)}**: ${count} 条\n`;
  });
  markdown += '\n';

  // 详细记录
  markdown += '## 📝 详细记录\n\n';

  applications.forEach((app, index) => {
    markdown += `### ${index + 1}. ${app.company_name} - ${app.position}\n\n`;
    
    markdown += '| 字段 | 内容 |\n';
    markdown += '|------|------|\n';
    markdown += `| 公司名称 | ${app.company_name || '-'} |\n`;
    markdown += `| 职位 | ${app.position || '-'} |\n`;
    markdown += `| 投递日期 | ${dayjs(app.application_date).format('YYYY-MM-DD')} |\n`;
    markdown += `| 当前状态 | ${getStatusLabel(app.status)} |\n`;
    
    if (app.salary_range) {
      markdown += `| 薪资范围 | ${app.salary_range} |\n`;
    }
    
    if (app.work_location) {
      markdown += `| 工作地点 | ${app.work_location} |\n`;
    }
    
    if (app.referrer_name) {
      markdown += `| 内推人 | ${app.referrer_name} |\n`;
    }
    
    if (app.referrer_contact) {
      markdown += `| 内推人联系方式 | ${app.referrer_contact} |\n`;
    }
    
    if (app.application_link) {
      markdown += `| 投递链接 | [查看链接](${app.application_link.startsWith('http') ? app.application_link : `https://${app.application_link}`}) |\n`;
    }
    
    if (app.company_industry) {
      markdown += `| 公司行业 | ${app.company_industry} |\n`;
    }
    
    if (app.company_location) {
      markdown += `| 公司地点 | ${app.company_location} |\n`;
    }
    
    markdown += '\n';
    
    if (app.requirements_keywords) {
      markdown += '**岗位要求关键词:**\n';
      markdown += `${app.requirements_keywords}\n\n`;
    }
    
    if (app.job_description) {
      markdown += '**职位描述:**\n';
      markdown += `${app.job_description}\n\n`;
    }
    
    if (app.notes) {
      markdown += '**备注:**\n';
      markdown += `${app.notes}\n\n`;
    }
    
    markdown += '---\n\n';
  });

  return markdown;
};

export const exportSingleToMarkdown = (application: Application): string => {
  const getStatusLabel = (status: string) => {
    const option = STATUS_OPTIONS.find(opt => opt.value === status);
    return option ? option.label : status;
  };

  let markdown = `# ${application.company_name} - ${application.position}\n\n`;
  markdown += `> 导出时间: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}\n\n`;

  markdown += '## 基本信息\n\n';
  markdown += '| 字段 | 内容 |\n';
  markdown += '|------|------|\n';
  markdown += `| 公司名称 | ${application.company_name || '-'} |\n`;
  markdown += `| 职位 | ${application.position || '-'} |\n`;
  markdown += `| 投递日期 | ${dayjs(application.application_date).format('YYYY-MM-DD')} |\n`;
  markdown += `| 当前状态 | ${getStatusLabel(application.status)} |\n`;
  
  if (application.salary_range) {
    markdown += `| 薪资范围 | ${application.salary_range} |\n`;
  }
  
  if (application.work_location) {
    markdown += `| 工作地点 | ${application.work_location} |\n`;
  }
  
  if (application.referrer_name) {
    markdown += `| 内推人 | ${application.referrer_name} |\n`;
  }
  
  if (application.referrer_contact) {
    markdown += `| 内推人联系方式 | ${application.referrer_contact} |\n`;
  }
  
  if (application.application_link) {
    markdown += `| 投递链接 | [查看链接](${application.application_link.startsWith('http') ? application.application_link : `https://${application.application_link}`}) |\n`;
  }
  
  if (application.company_industry) {
    markdown += `| 公司行业 | ${application.company_industry} |\n`;
  }
  
  if (application.company_location) {
    markdown += `| 公司地点 | ${application.company_location} |\n`;
  }
  
  markdown += '\n';
  
  if (application.requirements_keywords) {
    markdown += '## 岗位要求关键词\n\n';
    markdown += `${application.requirements_keywords}\n\n`;
  }
  
  if (application.job_description) {
    markdown += '## 职位描述\n\n';
    markdown += `${application.job_description}\n\n`;
  }
  
  if (application.notes) {
    markdown += '## 备注\n\n';
    markdown += `${application.notes}\n\n`;
  }

  return markdown;
};

export const downloadMarkdown = (content: string, filename: string = 'job_applications') => {
  const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename}_${dayjs().format('YYYY-MM-DD')}.md`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export const copyToClipboard = async (content: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(content);
    return true;
  } catch (err) {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = content;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return true;
    } catch (err) {
      document.body.removeChild(textArea);
      return false;
    }
  }
};
