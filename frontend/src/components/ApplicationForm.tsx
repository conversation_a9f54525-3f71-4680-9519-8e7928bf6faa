import React, { useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  message,
} from 'antd';
import dayjs from 'dayjs';
import { Application, STATUS_OPTIONS } from '../types';
import { applicationAPI } from '../services/api';

const { TextArea } = Input;

interface ApplicationFormProps {
  visible: boolean;
  application?: Application | null;
  onCancel: () => void;
  onSubmit: () => void;
}

const ApplicationForm: React.FC<ApplicationFormProps> = ({
  visible,
  application,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);

  useEffect(() => {
    if (visible && application) {
      form.setFieldsValue({
        ...application,
        application_date: application.application_date ? dayjs(application.application_date) : null,
      });
    } else if (visible) {
      form.resetFields();
      form.setFieldsValue({
        application_date: dayjs(),
        status: 'applied',
      });
    }
  }, [visible, application, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const formData = {
        ...values,
        application_date: values.application_date.format('YYYY-MM-DD'),
      };

      if (application?.id) {
        await applicationAPI.update(application.id, formData);
        message.success('更新成功');
      } else {
        await applicationAPI.create(formData);
        message.success('创建成功');
      }

      onSubmit();
    } catch (error: any) {
      if (error.errorFields) {
        message.error('请检查表单输入');
      } else {
        console.error('API Error:', error);
        const errorMessage = error.response?.data?.error || error.message || '操作失败';
        message.error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={application?.id ? '编辑投递记录' : '新增投递记录'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={800}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          status: 'applied',
          application_date: dayjs(),
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label={<span>公司名称 <span style={{ color: 'red' }}>*</span></span>}
              name="company_name"
              rules={[{ required: true, message: '请输入公司名称' }]}
            >
              <Input placeholder="请输入公司名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={<span>职位 <span style={{ color: 'red' }}>*</span></span>}
              name="position"
              rules={[{ required: true, message: '请输入职位名称' }]}
            >
              <Input placeholder="请输入职位名称" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              label={<span>投递日期 <span style={{ color: 'red' }}>*</span></span>}
              name="application_date"
              rules={[{ required: true, message: '请选择投递日期' }]}
            >
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label={<span>状态 <span style={{ color: 'red' }}>*</span></span>}
              name="status"
              rules={[{ required: true, message: '请选择状态' }]}
            >
              <Select placeholder="请选择状态">
                {STATUS_OPTIONS.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="薪资范围" name="salary_range">
              <Input placeholder="例如：15-25k" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="工作地点" name="work_location">
              <Input placeholder="请输入工作地点" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="内推人" name="referrer_name">
              <Input placeholder="请输入内推人姓名" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="内推人联系方式" name="referrer_contact">
              <Input placeholder="请输入内推人联系方式" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="公司行业" name="company_industry">
              <Input placeholder="请输入公司行业" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item label="投递链接/岗位信息链接" name="application_link">
          <Input
            placeholder="请输入岗位链接或状态查询链接"
            addonBefore="https://"
          />
        </Form.Item>

        <Form.Item label="岗位要求关键词" name="requirements_keywords">
          <TextArea
            rows={3}
            placeholder="请输入岗位要求的关键词，便于面试前复习"
          />
        </Form.Item>

        <Form.Item label="职位描述" name="job_description">
          <TextArea
            rows={4}
            placeholder="请输入职位描述"
          />
        </Form.Item>

        <Form.Item label="备注" name="notes">
          <TextArea
            rows={3}
            placeholder="请输入其他需要记录的信息"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ApplicationForm;