import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  DatePicker,
  Form,
  Row,
  Col,
  message,
  Popconfirm,
  Typography,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { Application, STATUS_OPTIONS } from '../types';
import { applicationAPI } from '../services/api';
import ApplicationForm from './ApplicationForm';
import { exportToMarkdown, exportSingleToMarkdown, downloadMarkdown, copyToClipboard } from '../utils/exportUtils';

const { RangePicker } = DatePicker;
const { Title } = Typography;

const ApplicationTable: React.FC = () => {
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  const [editingApplication, setEditingApplication] = useState<Application | null>(null);
  const [searchForm] = Form.useForm();

  const fetchApplications = async (filters?: any) => {
    setLoading(true);
    try {
      const response = await applicationAPI.getAll(filters);
      setApplications(response.data);
    } catch (error) {
      message.error('获取投递记录失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApplications();
  }, []);

  const handleSearch = (values: any) => {
    const filters: any = {};
    
    if (values.company) filters.company = values.company;
    if (values.position) filters.position = values.position;
    if (values.status) filters.status = values.status;
    if (values.dateRange) {
      filters.dateFrom = values.dateRange[0].format('YYYY-MM-DD');
      filters.dateTo = values.dateRange[1].format('YYYY-MM-DD');
    }

    fetchApplications(filters);
  };

  const handleReset = () => {
    searchForm.resetFields();
    fetchApplications();
  };

  const handleEdit = (record: Application) => {
    setEditingApplication(record);
    setFormVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await applicationAPI.delete(id);
      message.success('删除成功');
      fetchApplications();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleFormSubmit = () => {
    setFormVisible(false);
    setEditingApplication(null);
    fetchApplications();
  };

  const handleExportAll = () => {
    const markdown = exportToMarkdown(applications);
    downloadMarkdown(markdown, 'all_applications');
    message.success('导出成功');
  };

  const handleCopyAll = async () => {
    const markdown = exportToMarkdown(applications);
    const success = await copyToClipboard(markdown);
    if (success) {
      message.success('已复制到剪贴板');
    } else {
      message.error('复制失败');
    }
  };

  const handleExportSingle = (record: Application) => {
    const markdown = exportSingleToMarkdown(record);
    downloadMarkdown(markdown, `${record.company_name}_${record.position}`);
    message.success('导出成功');
  };

  const handleCopySingle = async (record: Application) => {
    const markdown = exportSingleToMarkdown(record);
    const success = await copyToClipboard(markdown);
    if (success) {
      message.success('已复制到剪贴板');
    } else {
      message.error('复制失败');
    }
  };

  const getStatusTag = (status: string) => {
    const statusOption = STATUS_OPTIONS.find(option => option.value === status);
    return (
      <Tag color={statusOption?.color || 'default'}>
        {statusOption?.label || status}
      </Tag>
    );
  };

  const columns: ColumnsType<Application> = [
    {
      title: '公司名称',
      dataIndex: 'company_name',
      key: 'company_name',
      width: 150,
      ellipsis: true,
    },
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
      width: 200,
      ellipsis: true,
    },
    {
      title: '投递日期',
      dataIndex: 'application_date',
      key: 'application_date',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
      sorter: (a, b) => dayjs(a.application_date).unix() - dayjs(b.application_date).unix(),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => getStatusTag(status),
      filters: STATUS_OPTIONS.map(option => ({
        text: option.label,
        value: option.value,
      })),
      onFilter: (value, record) => record.status === value,
    },
    {
      title: '薪资范围',
      dataIndex: 'salary_range',
      key: 'salary_range',
      width: 120,
      ellipsis: true,
    },
    {
      title: '工作地点',
      dataIndex: 'work_location',
      key: 'work_location',
      width: 120,
      ellipsis: true,
    },
    {
      title: '内推人',
      dataIndex: 'referrer_name',
      key: 'referrer_name',
      width: 100,
      ellipsis: true,
    },
    {
      title: '投递链接',
      dataIndex: 'application_link',
      key: 'application_link',
      width: 120,
      render: (link: string) => link ? (
        <a href={link.startsWith('http') ? link : `https://${link}`} target="_blank" rel="noopener noreferrer">
          查看链接
        </a>
      ) : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small" wrap>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
          >
            编辑
          </Button>
          <Button
            type="link"
            icon={<DownloadOutlined />}
            onClick={() => handleExportSingle(record)}
            size="small"
          >
            导出
          </Button>
          <Button
            type="link"
            icon={<CopyOutlined />}
            onClick={() => handleCopySingle(record)}
            size="small"
          >
            复制
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.id!)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={3}>投递记录管理</Title>
      
      {/* Search Form */}
      <Form
        form={searchForm}
        onFinish={handleSearch}
        className="search-form"
        layout="vertical"
      >
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item label="公司名称" name="company">
              <Input placeholder="请输入公司名称" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="职位" name="position">
              <Input placeholder="请输入职位名称" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="状态" name="status">
              <Select placeholder="请选择状态" allowClear>
                {STATUS_OPTIONS.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="投递日期" name="dateRange">
              <RangePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                搜索
              </Button>
              <Button onClick={handleReset} icon={<ReloadOutlined />}>
                重置
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setFormVisible(true)}
              >
                新增投递
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExportAll}
                disabled={applications.length === 0}
              >
                导出全部
              </Button>
              <Button
                icon={<CopyOutlined />}
                onClick={handleCopyAll}
                disabled={applications.length === 0}
              >
                复制全部
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>

      {/* Table */}
      <div className="table-container">
        <Table
          columns={columns}
          dataSource={applications}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </div>

      {/* Form Modal */}
      <ApplicationForm
        visible={formVisible}
        application={editingApplication}
        onCancel={() => {
          setFormVisible(false);
          setEditingApplication(null);
        }}
        onSubmit={handleFormSubmit}
      />
    </div>
  );
};

export default ApplicationTable;