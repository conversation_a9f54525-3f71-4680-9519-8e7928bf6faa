export interface Company {
  id?: number;
  name: string;
  industry?: string;
  size?: string;
  location?: string;
  website?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Application {
  id?: number;
  company_id: number;
  position: string;
  application_date: string;
  status: ApplicationStatus;
  salary_range?: string;
  work_location?: string;
  job_description?: string;
  requirements_keywords?: string;
  referrer_name?: string;
  referrer_contact?: string;
  application_link?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
  company_name?: string;
  company_industry?: string;
  company_location?: string;
}

export interface WrittenTest {
  id?: number;
  application_id: number;
  test_date?: string;
  test_format?: string;
  test_type?: string;
  key_topics?: string;
  duration_minutes?: number;
  result?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Interview {
  id?: number;
  application_id: number;
  round_number: number;
  round_name?: string;
  interview_date?: string;
  interviewer_name?: string;
  interviewer_position?: string;
  interview_format?: string;
  technical_questions?: string;
  project_questions?: string;
  behavioral_questions?: string;
  my_questions?: string;
  self_assessment?: string;
  result?: string;
  feedback?: string;
  created_at?: string;
  updated_at?: string;
}

export type ApplicationStatus = 
  | 'applied' 
  | 'screening' 
  | 'written_test' 
  | 'interview' 
  | 'final_interview' 
  | 'offer' 
  | 'rejected' 
  | 'withdrawn';

export interface ApplicationWithDetails extends Application {
  written_tests?: WrittenTest[];
  interviews?: Interview[];
}

export const STATUS_OPTIONS = [
  { value: 'applied', label: '已投递', color: 'blue' },
  { value: 'screening', label: '简历筛选', color: 'cyan' },
  { value: 'written_test', label: '笔试', color: 'orange' },
  { value: 'interview', label: '面试', color: 'purple' },
  { value: 'final_interview', label: '终面', color: 'magenta' },
  { value: 'offer', label: 'Offer', color: 'green' },
  { value: 'rejected', label: '被拒', color: 'red' },
  { value: 'withdrawn', label: '已撤回', color: 'default' },
];