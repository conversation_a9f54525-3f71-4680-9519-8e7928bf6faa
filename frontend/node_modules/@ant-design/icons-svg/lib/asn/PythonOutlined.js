"use strict";
// This icon file is generated automatically.
Object.defineProperty(exports, "__esModule", { value: true });
var PythonOutlined = { "icon": { "tag": "svg", "attrs": { "fill-rule": "evenodd", "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M555 790.5a28.5 28.5 0 1057 0 28.5 28.5 0 00-57 0m-143-557a28.5 28.5 0 1057 0 28.5 28.5 0 00-57 0" } }, { "tag": "path", "attrs": { "d": "M821.52 297.71H726.3v-95.23c0-49.9-40.58-90.48-90.48-90.48H388.19c-49.9 0-90.48 40.57-90.48 90.48v95.23h-95.23c-49.9 0-90.48 40.58-90.48 90.48v247.62c0 49.9 40.57 90.48 90.48 90.48h95.23v95.23c0 49.9 40.58 90.48 90.48 90.48h247.62c49.9 0 90.48-40.57 90.48-90.48V726.3h95.23c49.9 0 90.48-40.58 90.48-90.48V388.19c0-49.9-40.57-90.48-90.48-90.48M202.48 669.14a33.37 33.37 0 01-33.34-33.33V388.19a33.37 33.37 0 0133.34-33.33h278.57a28.53 28.53 0 0028.57-28.57 28.53 28.53 0 00-28.57-28.58h-126.2v-95.23a33.37 33.37 0 0133.34-33.34h247.62a33.37 33.37 0 0133.33 33.34v256.47a24.47 24.47 0 01-24.47 24.48H379.33c-45.04 0-81.62 36.66-81.62 81.62v104.1zm652.38-33.33a33.37 33.37 0 01-33.34 33.33H542.95a28.53 28.53 0 00-28.57 28.57 28.53 28.53 0 0028.57 28.58h126.2v95.23a33.37 33.37 0 01-33.34 33.34H388.19a33.37 33.37 0 01-33.33-33.34V565.05a24.47 24.47 0 0124.47-24.48h265.34c45.04 0 81.62-36.67 81.62-81.62v-104.1h95.23a33.37 33.37 0 0133.34 33.34z" } }] }, "name": "python", "theme": "outlined" };
exports.default = PythonOutlined;
