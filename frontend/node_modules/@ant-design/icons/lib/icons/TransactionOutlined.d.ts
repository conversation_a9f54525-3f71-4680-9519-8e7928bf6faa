import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![transaction](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY2OC42IDMyMGMwLTQuNC0zLjYtOC04LThoLTU0LjVjLTMgMC01LjggMS43LTcuMSA0LjRsLTg0LjcgMTY4LjhINTExbC04NC43LTE2OC44YTggOCAwIDAwLTcuMS00LjRoLTU1LjdjLTEuMyAwLTIuNi4zLTMuOCAxLTMuOSAyLjEtNS4zIDctMy4yIDEwLjhsMTAzLjkgMTkxLjZoLTU3Yy00LjQgMC04IDMuNi04IDh2MjcuMWMwIDQuNCAzLjYgOCA4IDhoNzZ2MzloLTc2Yy00LjQgMC04IDMuNi04IDh2MjcuMWMwIDQuNCAzLjYgOCA4IDhoNzZWNzA0YzAgNC40IDMuNiA4IDggOGg0OS45YzQuNCAwIDgtMy42IDgtOHYtNjMuNWg3Ni4zYzQuNCAwIDgtMy42IDgtOHYtMjcuMWMwLTQuNC0zLjYtOC04LThoLTc2LjN2LTM5aDc2LjNjNC40IDAgOC0zLjYgOC04di0yNy4xYzAtNC40LTMuNi04LTgtOEg1NjRsMTAzLjctMTkxLjZjLjUtMS4xLjktMi40LjktMy43ek0xNTcuOSA1MDQuMmEzNTIuNyAzNTIuNyAwIDAxMTAzLjUtMjQyLjRjMzIuNS0zMi41IDcwLjMtNTguMSAxMTIuNC03NS45IDQzLjYtMTguNCA4OS45LTI3LjggMTM3LjYtMjcuOCA0Ny44IDAgOTQuMSA5LjMgMTM3LjYgMjcuOCA0Mi4xIDE3LjggNzkuOSA0My40IDExMi40IDc1LjkgMTAgMTAgMTkuMyAyMC41IDI3LjkgMzEuNGwtNTAgMzkuMWE4IDggMCAwMDMgMTQuMWwxNTYuOCAzOC4zYzUgMS4yIDkuOS0yLjYgOS45LTcuN2wuOC0xNjEuNWMwLTYuNy03LjctMTAuNS0xMi45LTYuM2wtNDcuOCAzNy40Qzc3MC43IDE0Ni4zIDY0OC42IDgyIDUxMS41IDgyIDI3NyA4MiA4Ni4zIDI3MC4xIDgyIDUwMy44YTggOCAwIDAwOCA4LjJoNjBjNC4zIDAgNy44LTMuNSA3LjktNy44ek05MzQgNTEyaC02MGMtNC4zIDAtNy45IDMuNS04IDcuOGEzNTIuNyAzNTIuNyAwIDAxLTEwMy41IDI0Mi40IDM1Mi41NyAzNTIuNTcgMCAwMS0xMTIuNCA3NS45Yy00My42IDE4LjQtODkuOSAyNy44LTEzNy42IDI3LjhzLTk0LjEtOS4zLTEzNy42LTI3LjhhMzUyLjU3IDM1Mi41NyAwIDAxLTExMi40LTc1LjljLTEwLTEwLTE5LjMtMjAuNS0yNy45LTMxLjRsNDkuOS0zOS4xYTggOCAwIDAwLTMtMTQuMWwtMTU2LjgtMzguM2MtNS0xLjItOS45IDIuNi05LjkgNy43bC0uOCAxNjEuN2MwIDYuNyA3LjcgMTAuNSAxMi45IDYuM2w0Ny44LTM3LjRDMjUzLjMgODc3LjcgMzc1LjQgOTQyIDUxMi41IDk0MiA3NDcgOTQyIDkzNy43IDc1My45IDk0MiA1MjAuMmE4IDggMCAwMC04LTguMnoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
