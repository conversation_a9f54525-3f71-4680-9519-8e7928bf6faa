import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![pay-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xNjYuNiAyNDYuOEw1NjcuNSA1MTUuNmg2MmM0LjQgMCA4IDMuNiA4IDh2MjkuOWMwIDQuNC0zLjYgOC04IDhoLTgyVjYwM2g4MmM0LjQgMCA4IDMuNiA4IDh2MjkuOWMwIDQuNC0zLjYgOC04IDhoLTgyVjcxN2MwIDQuNC0zLjYgOC04IDhoLTU0LjNjLTQuNCAwLTgtMy42LTgtOHYtNjguMWgtODEuN2MtNC40IDAtOC0zLjYtOC04VjYxMWMwLTQuNCAzLjYtOCA4LThoODEuN3YtNDEuNWgtODEuN2MtNC40IDAtOC0zLjYtOC04di0yOS45YzAtNC40IDMuNi04IDgtOGg2MS40TDM0NS40IDMxMC44YTguMDcgOC4wNyAwIDAxNy0xMS45aDYwLjdjMyAwIDUuOCAxLjcgNy4xIDQuNGw5MC42IDE4MGgzLjRsOTAuNi0xODBhOCA4IDAgMDE3LjEtNC40aDU5LjVjNC40IDAgOCAzLjYgOCA4IC4yIDEuNC0uMiAyLjctLjggMy45eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
