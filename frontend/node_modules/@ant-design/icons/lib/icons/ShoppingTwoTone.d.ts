import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![shopping](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY5NiA0NzJjMCA0LjQtMy42IDgtOCA4aC01NmMtNC40IDAtOC0zLjYtOC04di04OEg0MDB2ODhjMCA0LjQtMy42IDgtOCA4aC01NmMtNC40IDAtOC0zLjYtOC04di04OGgtOTZ2NDU2aDU2MFYzODRoLTk2djg4eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODMyIDMxMkg2OTZ2LTE2YzAtMTAxLjYtODIuNC0xODQtMTg0LTE4NHMtMTg0IDgyLjQtMTg0IDE4NHYxNkgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjUzNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDMyLTE2YzAtNjEuOSA1MC4xLTExMiAxMTItMTEyczExMiA1MC4xIDExMiAxMTJ2MTZINDAwdi0xNnptMzkyIDU0NEgyMzJWMzg0aDk2djg4YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTg4aDIyNHY4OGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di04OGg5NnY0NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
